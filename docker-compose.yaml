services:
   #  # PostgreSQL для User Service
   #  postgres:
   #    image: postgres:14
   #    container_name: postgres
   #    environment:
   #      POSTGRES_DB: edunite
   #      POSTGRES_USER: olzzhas
   #      POSTGRES_PASSWORD: Olzhas040404
   #    ports:
   #      - "5432:5432"
   #    volumes:
   #      - postgres_user_data:/var/lib/postgresql/data
   #    healthcheck:
   #      test: ["CMD-SHELL", "pg_isready -U olzzhas -d edunite"]
   #      interval: 10s
   #      timeout: 5s
   #      retries: 5
   #    networks:
   #      - dev

   flyway:
      image: flyway/flyway:9.2.1
      command: >
         -url=**********************************************
         -user=olzzhas
         -password=Olzhas123451
         -connectRetries=5
         -locations=filesystem:/flyway/sql
         migrate
      environment:
         - FLYWAY_OUT_OF_ORDER=true
      volumes:
         - ./migrations:/flyway/sql
      networks:
         - dev

   # User Service
   user_service:
      build:
         context: ./user_service
      environment:
         DATABASE_URL: ******************************************************/edunite
      env_file:
         - ./user_service/.env
      ports:
         - "50051:50051"
      depends_on:
         - flyway
      networks:
         - dev

   rabbitmq:
      image: rabbitmq:3-management
      container_name: rabbitmq_edunite
      environment:
         RABBITMQ_DEFAULT_USER: guest
         RABBITMQ_DEFAULT_PASS: guest
         RABBITMQ_ERLANG_COOKIE: "a22gbda9w1dcnaosx10857nuwnadncu"
      ports:
         - "5673:5672"
         - "15673:15672"
      healthcheck:
         test: ["CMD", "rabbitmqctl", "status"]
         interval: 10s
         timeout: 5s
         retries: 5
      networks:
         - dev
      logging:
         driver: "none"

   mongodb:
      image: mongo:5
      ports:
         - "27018:27017"
      volumes:
         - mongo_data:/data/db
      healthcheck:
         test: ["CMD", "mongosh", "--eval", "db.runCommand({ ping: 1 })"]
         interval: 10s
         timeout: 5s
         retries: 5
      networks:
         - dev
      logging:
         driver: "none"

   # Course Service
   course_service:
      build:
         context: ./course_service
      environment:
         DATABASE_URL: ******************************************************/edunite
      env_file:
         - ./course_service/.env
      ports:
         - "50053:50053"
      depends_on:
         - flyway
      networks:
         - dev

   logger_service:
      build:
         context: ./logger_service
      environment:
         MONGO_URI: "mongodb://mongodb:27017"
         RABBITMQ_URI: "amqp://guest:guest@rabbitmq:5672/"
      depends_on:
         rabbitmq:
            condition: service_healthy
         mongodb:
            condition: service_healthy
      networks:
         - dev

   # Auth Service
   auth_service:
      build:
         context: ./auth_service
      environment:
         DATABASE_URL: ******************************************************/edunite
      env_file:
         - ./auth_service/.env
      ports:
         - "50052:50052"
      depends_on:
         - flyway
      networks:
         - dev

   gateway:
      build:
         context: .
         dockerfile: gateway/Dockerfile
      environment:
         USER_SERVICE_URL: "user_service:50051"
         AUTH_SERVICE_URL: "auth_service:50052"
         COURSE_SERVICE_URL: "course_service:50053"
         SPORT_SERVICE_URL: "sport_service:50054"
         STORAGE_SERVICE_URL: "storage_service:50059"
         LOGGER_SERVICE_URL: "logger_service:50055"
         NOTIFICATION_SERVICE_URL: "notification_service:50056"
         RABBIT_MQ_SERVICE_URL: "amqp://guest:guest@rabbitmq:5672/"
      ports:
         - "8081:8081"
      depends_on:
         rabbitmq:
            condition: service_started
         user_service:
            condition: service_started
         auth_service:
            condition: service_started
         course_service:
            condition: service_started
         sport_service:
            condition: service_started
         storage_service:
            condition: service_started
         logger_service:
            condition: service_started
         notification_service:
            condition: service_started
      restart: on-failure
      networks:
         - dev

   # -------------------------
   # MinIO (хранение файлов)
   # -------------------------
   minio:
      image: minio/minio:latest
      container_name: minio
      environment:
         MINIO_ROOT_USER: minioadmin
         MINIO_ROOT_PASSWORD: minioadmin
      command: server /data --console-address ":9001"
      ports:
         - "9010:9000" # API Port (exposed to public)
         - "9011:9001" # Console (exposed to public)
      volumes:
         - minio_data:/data
      healthcheck:
         test: ["CMD", "curl", "-f", "http://minio:9000/minio/health/live"]
         interval: 10s
         timeout: 5s
         retries: 5
      restart: always
      networks:
         - dev

   # -------------------------
   # Storage Service (gRPC)
   # -------------------------
   storage_service:
      platform: linux/amd64
      build:
         context: ./storage_service
      env_file:
         - ./.env
      environment:
         # MinIO configuration (legacy)
         MINIO_ENDPOINT: "minio:9000"
         MINIO_ACCESS_KEY: "minioadmin"
         MINIO_SECRET_KEY: "minioadmin"
         MINIO_USE_SSL: "false"
         # Storage configuration
         STORAGE_TYPE: "local"
         AWS_REGION: "us-east-1"
      ports:
         - "50059:50059"
      volumes:
         - storage_data:/app/storage
      depends_on:
         minio:
            condition: service_healthy
      restart: on-failure
      networks:
         - dev

   sport_service:
      build:
         context: ./sport_service # каталог с Dockerfile
      environment:
         DATABASE_URL: ******************************************************/edunite
      env_file:
         - ./sport_service/.env # если читается внутри main.go
      ports:
         - "50054:50054"
      depends_on:
         flyway: # ждём миграций
            condition: service_completed_successfully
      networks:
         - dev

   notification_service:
      build:
         context: ./notification_service
      environment:
         DATABASE_URL: ******************************************************/edunite
      env_file:
         - ./notification_service/.env
      ports:
         - "50056:50056"
      depends_on:
         flyway:
            condition: service_completed_successfully
      networks:
         - dev

   # -------------------------
   # Redis для кэша и сессий
   # -------------------------
   redis:
      image: redis:latest
      container_name: redis_edunite
      ports:
         - "6400:6400"
      networks:
         - dev

   # -------------------------
   # Prometheus для мониторинга
   # -------------------------
   prometheus:
      image: prom/prometheus:latest
      container_name: prometheus_edunite
      command:
         - "--config.file=/etc/prometheus/prometheus.yml"
         - "--storage.tsdb.path=/prometheus"
         - "--web.console.libraries=/etc/prometheus/console_libraries"
         - "--web.console.templates=/etc/prometheus/consoles"
         - "--storage.tsdb.retention.time=200h"
         - "--web.enable-lifecycle"
      ports:
         - "9090:9090"
      volumes:
         - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
         - prometheus_data:/prometheus
      networks:
         - dev
      restart: unless-stopped

   # -------------------------
   # Grafana для визуализации
   # -------------------------
   grafana:
      image: grafana/grafana:latest
      container_name: grafana_edunite
      environment:
         GF_SECURITY_ADMIN_USER: admin
         GF_SECURITY_ADMIN_PASSWORD: admin
         GF_USERS_ALLOW_SIGN_UP: false
      ports:
         - "3000:3000"
      volumes:
         - grafana_data:/var/lib/grafana
         - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      depends_on:
         - prometheus
      networks:
         - dev
      restart: unless-stopped

#  nginx:
#    image: nginx:latest
#    container_name: nginx_gateway
#    volumes:
#      - ./nginx.conf:/etc/nginx/conf.d/default.conf:ro
#    ports:
#      - "80:80"
#    depends_on:
#      - gateway
#    networks:
#      - dev
#

volumes:
   mongo_data:
   minio_data:
   storage_data:

networks:
   dev:
      external: true
