# Мониторинг EduNite

Этот каталог содержит конфигурацию для мониторинга системы EduNite с использованием Prometheus и Grafana.

## Компоненты

### Prometheus
- **Порт**: 9090
- **URL**: http://localhost:9090
- **Описание**: Система сбора и хранения метрик

### Grafana
- **Порт**: 3000
- **URL**: http://localhost:3000
- **Логин**: admin
- **Пароль**: admin
- **Описание**: Система визуализации метрик

## Запуск

```bash
docker-compose up -d prometheus grafana
```

## Конфигурация

### Prometheus
- Конфигурация находится в `prometheus.yml`
- Собирает метрики с всех сервисов EduNite
- Интервал сбора: 15 секунд
- Хранение данных: 200 часов

### Grafana
- Автоматически настроен источник данных Prometheus
- Включает базовый дашборд "EduNite Overview"
- Данные сохраняются в Docker volume

## Мониторируемые сервисы

1. **Gateway** (порт 8081)
2. **User Service** (порт 50051)
3. **Auth Service** (порт 50052)
4. **Course Service** (порт 50053)
5. **Sport Service** (порт 50054)
6. **Storage Service** (порт 50059)
7. **Notification Service** (порт 50056)
8. **RabbitMQ** (порт 15692)
9. **MinIO** (порт 9000)

## Добавление метрик в сервисы

Для полноценного мониторинга рекомендуется добавить экспорт метрик в ваши Go сервисы:

```go
import (
    "github.com/prometheus/client_golang/prometheus"
    "github.com/prometheus/client_golang/prometheus/promhttp"
)

// В main функции
http.Handle("/metrics", promhttp.Handler())
```

## Полезные запросы Prometheus

- `up` - статус всех сервисов
- `up{job="gateway"}` - статус gateway
- `prometheus_tsdb_head_samples_appended_total` - количество собранных метрик

## Дашборды

### EduNite Overview
Базовый дашборд включает:
- Статус всех сервисов
- Здоровье основных компонентов
- Временные графики доступности

## Расширение мониторинга

Для расширения мониторинга можно добавить:
- Node Exporter для метрик системы
- PostgreSQL Exporter для метрик базы данных
- Redis Exporter для метрик Redis
- Alertmanager для уведомлений

## Troubleshooting

1. **Prometheus не видит сервисы**: Проверьте, что сервисы экспортируют метрики на `/metrics`
2. **Grafana не показывает данные**: Убедитесь, что Prometheus собирает данные
3. **Нет доступа к Grafana**: Проверьте, что порт 3000 не занят другим приложением
