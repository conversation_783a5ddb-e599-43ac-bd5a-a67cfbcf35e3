global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus сам себя
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Gateway service
  - job_name: 'gateway'
    static_configs:
      - targets: ['gateway:8081']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # User service (если есть метрики)
  - job_name: 'user-service'
    static_configs:
      - targets: ['user_service:50051']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Auth service (если есть метрики)
  - job_name: 'auth-service'
    static_configs:
      - targets: ['auth_service:50052']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Course service (если есть метрики)
  - job_name: 'course-service'
    static_configs:
      - targets: ['course_service:50053']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Sport service (если есть метрики)
  - job_name: 'sport-service'
    static_configs:
      - targets: ['sport_service:50054']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Storage service (если есть метрики)
  - job_name: 'storage-service'
    static_configs:
      - targets: ['storage_service:50059']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Notification service (если есть метрики)
  - job_name: 'notification-service'
    static_configs:
      - targets: ['notification_service:50056']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # PostgreSQL (если есть postgres_exporter)
  # - job_name: 'postgres'
  #   static_configs:
  #     - targets: ['postgres_exporter:9187']

  # RabbitMQ (встроенные метрики)
  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['rabbitmq:15692']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # MongoDB (если есть mongodb_exporter)
  # - job_name: 'mongodb'
  #   static_configs:
  #     - targets: ['mongodb_exporter:9216']

  # MinIO (встроенные метрики)
  - job_name: 'minio'
    static_configs:
      - targets: ['minio:9000']
    metrics_path: '/minio/v2/metrics/cluster'
    scrape_interval: 30s

  # Redis (если есть redis_exporter)
  # - job_name: 'redis'
  #   static_configs:
  #     - targets: ['redis_exporter:9121']
